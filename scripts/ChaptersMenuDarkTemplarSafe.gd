extends Control

# Dark Templar Chapters Menu - Bezpečná verzia bez FontLoader

# Chapter Selection settings
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "VII"]

# Chapter data
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k <PERSON> Helsingovmu zámku.",
		"unlocked": true,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Interior_view_of_ornate_Victorian_horse-drawn_car_514b5c75-3caa-4a98-9002-82c1c9326dc1_0.png"
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "Vstup do zámku cez masívnu železnú bránu z<PERSON>ú heraldickými symbolmi.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_medieval_castle_gate_with_weathered_iron__ff9848cd-5fdb-41fe-b04c-027d685a6e1e_0.png"
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Prehľadávanie veľkej sály zámku a hľadanie stôp po Van Helsingovi.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Grand_medieval_great_hall_with_vaulted_stone_ceil_ab110728-e42f-4cb0-b8c7-c695e3b75b82_0.png"
	}
]

func _ready():
	print("🏰 Dark Templar Chapters Menu (Safe) načítané")
	
	# Bezpečné načítanie progress
	load_chapter_progress()
	
	# Bezpečné pripojenie signálov
	connect_signals()
	
	# Aktualizácia zobrazenia
	update_chapter_display()
	
	# Aplikovanie základných štýlov
	apply_basic_styles()

func load_chapter_progress():
	"""Načíta progress kapitol z GameManager"""
	if GameManager:
		for i in range(chapter_data.size()):
			var chapter = chapter_data[i]
			chapter.unlocked = GameManager.is_chapter_unlocked(chapter.number)
			chapter.completed = chapter.number in GameManager.completed_chapters

func connect_signals():
	"""Pripojí signály pre buttony"""
	var prev_btn = get_node_or_null("MainPanel/NavigationContainer/PrevButton")
	var next_btn = get_node_or_null("MainPanel/NavigationContainer/NextButton")
	var play_btn = get_node_or_null("MainPanel/NavigationContainer/PlayButton")
	var back_btn = get_node_or_null("MainPanel/BackButton")
	var close_btn = get_node_or_null("MainPanel/CloseButton")
	
	if prev_btn: prev_btn.pressed.connect(_on_prev_pressed)
	if next_btn: next_btn.pressed.connect(_on_next_pressed)
	if play_btn: play_btn.pressed.connect(_on_play_pressed)
	if back_btn: back_btn.pressed.connect(_on_back_pressed)
	if close_btn: close_btn.pressed.connect(_on_back_pressed)

func apply_basic_styles():
	"""Aplikuje základné štýly bez FontLoader"""
	var title_label = get_node_or_null("MainPanel/TitlePanel/TitleLabel")
	var chapter_title = get_node_or_null("MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterTitle")
	var play_label = get_node_or_null("MainPanel/NavigationContainer/PlayButton/PlayLabel")
	var back_label = get_node_or_null("MainPanel/BackButton/BackLabel")
	
	if title_label:
		title_label.add_theme_font_size_override("font_size", 28)
		title_label.add_theme_color_override("font_color", Color("#D4AF37"))
	
	if chapter_title:
		chapter_title.add_theme_font_size_override("font_size", 32)
		chapter_title.add_theme_color_override("font_color", Color("#D4AF37"))
	
	if play_label:
		play_label.add_theme_font_size_override("font_size", 20)
		play_label.add_theme_color_override("font_color", Color("#D4AF37"))
	
	if back_label:
		back_label.add_theme_font_size_override("font_size", 20)
		back_label.add_theme_color_override("font_color", Color("#D4AF37"))

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)
	
	# Update title
	var title_text = "EPILÓG" if is_epilogue else "KAPITOLA " + roman_numerals[current_chapter_index]
	var chapter_title = get_node_or_null("MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterTitle")
	if chapter_title: chapter_title.text = title_text
	
	# Update content
	var chapter_subtitle = get_node_or_null("MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterSubtitle")
	if chapter_subtitle: chapter_subtitle.text = chapter.title
	
	var chapter_description = get_node_or_null("MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterDescription")
	if chapter_description: chapter_description.text = chapter.description
	
	# Update preview image
	var chapter_image = get_node_or_null("MainPanel/ContentContainer/ChapterPanel/ChapterContent/ImageContainer/ImageFrame/ChapterImage")
	if chapter_image and ResourceLoader.exists(chapter.image_path):
		chapter_image.texture = load(chapter.image_path)
	
	# Update button states
	var prev_button = get_node_or_null("MainPanel/NavigationContainer/PrevButton")
	var next_button = get_node_or_null("MainPanel/NavigationContainer/NextButton")
	var play_button = get_node_or_null("MainPanel/NavigationContainer/PlayButton")
	var play_label = get_node_or_null("MainPanel/NavigationContainer/PlayButton/PlayLabel")
	
	if prev_button: prev_button.disabled = current_chapter_index == 0
	if next_button: next_button.disabled = current_chapter_index == (chapter_data.size() - 1)
	if play_button: play_button.disabled = not chapter.unlocked
	
	# Visual feedback pre unlocked/locked
	if play_label:
		if chapter.unlocked:
			play_label.text = "HRAŤ" if not chapter.completed else "HRAŤ ZNOVU"
			play_label.modulate = Color.WHITE
		else:
			play_label.text = "UZAMKNUTÉ"
			play_label.modulate = Color(0.5, 0.5, 0.5)

func _on_prev_pressed():
	"""Predchádzajúca kapitola"""
	if current_chapter_index > 0:
		if AudioManager: AudioManager.play_menu_button_sound()
		current_chapter_index -= 1
		update_chapter_display()

func _on_next_pressed():
	"""Nasledujúca kapitola"""
	if current_chapter_index < chapter_data.size() - 1:
		if AudioManager: AudioManager.play_menu_button_sound()
		current_chapter_index += 1
		update_chapter_display()

func _on_play_pressed():
	"""Spustenie kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	if chapter.unlocked:
		if AudioManager: AudioManager.play_menu_button_sound()
		var scene_path = "res://scenes/Chapter" + str(chapter.number) + ".tscn"
		get_tree().change_scene_to_file(scene_path)

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	if AudioManager: AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Klávesové skratky"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_left"):
		_on_prev_pressed()
	elif event.is_action_pressed("ui_right"):
		_on_next_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
