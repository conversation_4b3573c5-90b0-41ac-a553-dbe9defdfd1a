extends Control

# 🏰 BEZPEČNÝ VERTIKÁLNY CHAPTERS MENU - DARK TEMPLAR ŠTÝL
# Inšpirovaný obrázkom s vertikálnym layoutom - s bezpečným node prístupom

# Chapter Selection settings
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "Epilóg"]

# Chapter data - používame skutočné obrázky z pozadia priečinkov
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu cez temný les k hrôzostrašnému sídlu.",
		"unlocked": true,
		"completed": false,
		"image_path": "res://assets/Obrázky/Kapitola_1/1.png"
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "Vstup do zámku cez masívnu železnú bránu zdobenú heraldickými symbolmi. Vyriešte krvavý nápis a prejdite skúškou Rádu, aby ste sa dostali dovnútra.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/pozadia/Kapitola_2/1.png"
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Prehľadávanie veľkej sály zámku a hľadanie stôp po Van Helsingovi. Rozlúštite obrátený odkaz a vyriešte matematickú hádanku.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/pozadia/Kapitola_3/1.png"
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "Objavenie skrytého krídla zámku s tajomnou knižnicou. Vyriešte test pamäte a vampírsku aritmetiku.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/pozadia/Kapitola_4/1.png"
	},
	{
		"number": 5,
		"title": "TEMNÉ TAJOMSTVÁ",
		"description": "Odhalenie temných tajomstiev Van Helsingovej minulosti v jeho súkromných komnatách.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/pozadia/Kapitola_5/1.png"
	},
	{
		"number": 6,
		"title": "KONFRONTÁCIA",
		"description": "Finálna konfrontácia s Van Helsingom v jeho laboratóriu. Vyriešte logickú hádanku troch sestier a rytmus rituálu.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/pozadia/Kapitola_6/1.png"
	},
	{
		"number": 7,
		"title": "EPILÓG",
		"description": "Záverečné odhalenie pravdy o Van Helsingovi a ukončenie príbehu.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/pozadia/Kapitola_7/1.png"
	}
]

func _ready():
	print("🏰 Bezpečný Vertikálny Dark Templar Chapters Menu načítané")
	
	# Načítanie progress
	load_chapter_progress()
	
	# Pripojenie signálov
	connect_signals()
	
	# Aktualizácia zobrazenia
	update_chapter_display()
	
	# Aplikovanie štýlov
	apply_basic_styles()

func load_chapter_progress():
	"""Načítanie postupu z GameManager"""
	for i in range(chapter_data.size()):
		var chapter_num = i + 1
		if GameManager:
			chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
			chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func connect_signals():
	"""Pripojenie signálov pre buttony"""
	# Chapter buttons
	var chapter_button_paths = [
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter1Button",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter2Button",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter3Button",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter4Button",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter5Button",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter6Button",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/EpilogueButton"
	]
	
	for i in range(chapter_button_paths.size()):
		var btn = get_node_or_null(chapter_button_paths[i])
		if btn:
			btn.pressed.connect(_on_chapter_button_pressed.bind(i))
	
	# Other buttons
	var play_btn = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton")
	var back_btn = get_node_or_null("MainPanel/ContentContainer/BottomButtonsContainer/BackButton")
	
	if play_btn: play_btn.pressed.connect(_on_play_pressed)
	if back_btn: back_btn.pressed.connect(_on_back_pressed)

func apply_basic_styles():
	"""Aplikovanie základných štýlov"""
	var chapter_title = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterTitle")
	var chapter_subtitle = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterSubtitle")
	var play_label = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton/PlayLabel")
	var back_label = get_node_or_null("MainPanel/ContentContainer/BottomButtonsContainer/BackButton/BackLabel")
	var list_title = get_node_or_null("MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterListTitle")

	if chapter_title:
		chapter_title.add_theme_font_size_override("font_size", 28)
		chapter_title.add_theme_color_override("font_color", Color("#D4AF37"))

	if chapter_subtitle:
		chapter_subtitle.add_theme_font_size_override("font_size", 24)
		chapter_subtitle.add_theme_color_override("font_color", Color("#D4AF37"))

	if play_label:
		play_label.add_theme_font_size_override("font_size", 18)
		play_label.add_theme_color_override("font_color", Color.WHITE)

	if back_label:
		back_label.add_theme_font_size_override("font_size", 18)
		back_label.add_theme_color_override("font_color", Color.WHITE)

	if list_title:
		list_title.add_theme_font_size_override("font_size", 22)
		list_title.add_theme_color_override("font_color", Color("#D4AF37"))

	# Aplikovanie štýlov na chapter buttony pre lepšiu viditeľnosť
	apply_chapter_button_styles()

func apply_chapter_button_styles():
	"""Aplikovanie štýlov na chapter buttony pre lepšiu viditeľnosť"""
	var chapter_label_paths = [
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter1Button/Chapter1Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter2Button/Chapter2Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter3Button/Chapter3Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter4Button/Chapter4Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter5Button/Chapter5Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter6Button/Chapter6Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/EpilogueButton/EpilogueLabel"
	]

	for i in range(chapter_label_paths.size()):
		var label = get_node_or_null(chapter_label_paths[i])
		if label:
			# Väčší font a lepší kontrast
			label.add_theme_font_size_override("font_size", 18)
			label.add_theme_color_override("font_color", Color("#F5F5DC"))

			# Pridanie outline pre lepšiu čitateľnosť
			label.add_theme_constant_override("outline_size", 2)
			label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

			# Padding pre lepší vzhľad
			label.offset_left = 10
			label.offset_right = -10

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)
	
	# Update title
	var title_text = "EPILÓG" if is_epilogue else "KAPITOLA " + roman_numerals[current_chapter_index]
	var chapter_title = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterTitle")
	if chapter_title:
		chapter_title.text = title_text
	
	# Update content
	var chapter_subtitle = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterSubtitle")
	if chapter_subtitle:
		chapter_subtitle.text = chapter.title
	
	var chapter_description = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterDescription")
	if chapter_description:
		chapter_description.text = chapter.description
	
	# Update preview image
	var chapter_image = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/ImageContainer/ImageFrame/ChapterImage")
	if chapter_image:
		print("🖼️ Načítavam obrázok pre kapitolu ", chapter.number, ": ", chapter.image_path)
		if ResourceLoader.exists(chapter.image_path):
			var texture = load(chapter.image_path)
			chapter_image.texture = texture
			print("✅ Obrázok úspešne načítaný")
		else:
			print("❌ Obrázok neexistuje: ", chapter.image_path)
			chapter_image.texture = null
	
	# Update button states
	var play_button = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton")
	var play_label = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton/PlayLabel")
	
	if play_button:
		play_button.disabled = not chapter.unlocked
	
	# Visual feedback pre unlocked/locked
	if play_label:
		if chapter.unlocked:
			play_label.text = "HRAŤ" if not chapter.completed else "HRAŤ ZNOVU"
			play_label.modulate = Color.WHITE
		else:
			play_label.text = "UZAMKNUTÉ"
			play_label.modulate = Color(0.5, 0.5, 0.5)

	# Update progress bar
	update_progress_display(chapter)
	
	# Update chapter list highlighting
	update_chapter_list_highlighting()

func update_progress_display(chapter: Dictionary):
	"""Aktualizácia zobrazenia postupu"""
	var progress_label = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ProgressContainer/ProgressLabel")
	var progress_bar = get_node_or_null("MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ProgressContainer/ProgressBar")
	
	if not progress_label or not progress_bar:
		return
	
	if not chapter.unlocked:
		progress_label.text = "Postup: Uzamknuté"
		progress_bar.value = 0
		progress_bar.modulate = Color(0.5, 0.5, 0.5)
	elif chapter.completed:
		progress_label.text = "Postup: Dokončené ✓"
		progress_bar.value = 100
		progress_bar.modulate = Color(0.2, 0.8, 0.2)  # Zelená
	else:
		# Pre odomknuté ale nedokončené kapitoly
		var progress_value = 0
		if GameManager and GameManager.completed_puzzles.has(chapter.number):
			var completed_puzzles_count = GameManager.completed_puzzles[chapter.number].size()
			var total_puzzles = 2  # Väčšina kapitol má 2 hlavolamy
			if chapter.number == 7:  # Epilóg nemá hlavolamy
				total_puzzles = 1

			progress_value = int((float(completed_puzzles_count) / float(total_puzzles)) * 100.0)

		if progress_value == 0:
			progress_label.text = "Postup: Začaté"
			progress_value = 10  # Minimálny progress za začatie
		else:
			progress_label.text = "Postup: " + str(progress_value) + "%"

		progress_bar.value = progress_value
		progress_bar.modulate = Color(0.8, 0.6, 0.2)  # Zlatá

func update_chapter_list_highlighting():
	"""Aktualizácia zvýraznenia v zozname kapitol"""
	var chapter_label_paths = [
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter1Button/Chapter1Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter2Button/Chapter2Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter3Button/Chapter3Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter4Button/Chapter4Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter5Button/Chapter5Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter6Button/Chapter6Label",
		"MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/EpilogueButton/EpilogueLabel"
	]

	for i in range(chapter_label_paths.size()):
		var label = get_node_or_null(chapter_label_paths[i])
		if label:
			var chapter = chapter_data[i]

			# Zvýraznenie aktuálnej kapitoly
			if i == current_chapter_index:
				label.modulate = Color("#FFD700")  # Jasná zlatá
				label.add_theme_font_size_override("font_size", 20)  # Väčší font
				label.text = "► " + get_chapter_display_name(i)

				# Silnejší outline pre aktuálnu kapitolu
				label.add_theme_constant_override("outline_size", 3)
				label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 1.0))
			else:
				# Normálny font pre ostatné
				label.add_theme_font_size_override("font_size", 18)
				label.add_theme_constant_override("outline_size", 2)
				label.add_theme_color_override("font_outline_color", Color(0, 0, 0, 0.8))

				# Farba podľa stavu
				if chapter.completed:
					label.modulate = Color("#32CD32")  # Jasná zelená
					label.text = "✓ " + get_chapter_display_name(i)
				elif chapter.unlocked:
					label.modulate = Color("#F5F5DC")  # Krémová
					label.text = "▶ " + get_chapter_display_name(i)
				else:
					label.modulate = Color("#808080")  # Svetlejšia sivá
					label.text = "🔒 " + get_chapter_display_name(i)

func get_chapter_display_name(index: int) -> String:
	"""Získanie názvu kapitoly pre zobrazenie - krátke verzie"""
	var chapter = chapter_data[index]
	if index == chapter_data.size() - 1:
		return "Epilóg"
	else:
		# Krátke názvy pre lepšie zobrazenie
		var short_titles = [
			"Búrlivá cesta",
			"Brána zámku",
			"Pátranie",
			"Tajné krídlo",
			"Temné tajomstvá",
			"Konfrontácia"
		]
		return "Kap. " + roman_numerals[index] + " - " + short_titles[index]

func _on_chapter_button_pressed(chapter_index: int):
	"""Kliknutie na kapitolu v zozname"""
	if AudioManager: AudioManager.play_menu_button_sound()
	current_chapter_index = chapter_index
	update_chapter_display()

func _on_play_pressed():
	"""Spustenie kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	if chapter.unlocked:
		if AudioManager: AudioManager.play_menu_button_sound()
		var scene_path = "res://scenes/Chapter" + str(chapter.number) + ".tscn"
		get_tree().change_scene_to_file(scene_path)
	else:
		# Zvuk chyby pre uzamknuté kapitoly
		if AudioManager: AudioManager.play_puzzle_error_sound()

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	if AudioManager: AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Klávesové skratky"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
