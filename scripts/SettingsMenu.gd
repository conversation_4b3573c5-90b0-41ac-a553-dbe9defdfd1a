extends Control

# UI References - bud<PERSON> načítané v _ready()
var master_volume_slider: HSlider
var master_volume_label: Label
var music_volume_slider: HSlider
var music_volume_label: Label
var sfx_volume_slider: HSlider
var sfx_volume_label: Label
var fullscreen_button: CheckButton
var back_button: Button

func _ready():
	# Načítanie UI referencií
	setup_ui_references()

	# Kontrola dostupnosti nodov
	validate_node_references()

	# Nasta<PERSON>ie hodnôt sliderov
	if master_volume_slider:
		master_volume_slider.min_value = 0.0
		master_volume_slider.max_value = 1.0
		master_volume_slider.step = 0.1
		master_volume_slider.value = GameManager.game_settings.master_volume
	
	if music_volume_slider:
		music_volume_slider.min_value = 0.0
		music_volume_slider.max_value = 1.0
		music_volume_slider.step = 0.1
		music_volume_slider.value = GameManager.game_settings.music_volume

	if sfx_volume_slider:
		sfx_volume_slider.min_value = 0.0
		sfx_volume_slider.max_value = 1.0
		sfx_volume_slider.step = 0.1
		sfx_volume_slider.value = GameManager.game_settings.sfx_volume

	if fullscreen_button:
		fullscreen_button.button_pressed = GameManager.game_settings.fullscreen
	
	# Pripojenie signálov
	if master_volume_slider:
		master_volume_slider.value_changed.connect(_on_master_volume_changed)
	if music_volume_slider:
		music_volume_slider.value_changed.connect(_on_music_volume_changed)
	if sfx_volume_slider:
		sfx_volume_slider.value_changed.connect(_on_sfx_volume_changed)
	if fullscreen_button:
		fullscreen_button.toggled.connect(_on_fullscreen_toggled)
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
	
	# Aktualizovanie labelov
	update_volume_labels()

	# Nastavenie fokusu
	if master_volume_slider:
		master_volume_slider.grab_focus()

func setup_ui_references():
	"""Bezpečné načítanie UI referencií"""
	master_volume_slider = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/MasterVolumeContainer/MasterVolumeSlider")
	master_volume_label = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/MasterVolumeContainer/MasterVolumeLabel")
	music_volume_slider = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/MusicVolumeContainer/MusicVolumeSlider")
	music_volume_label = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/MusicVolumeContainer/MusicVolumeLabel")
	sfx_volume_slider = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/SFXVolumeContainer/SFXVolumeSlider")
	sfx_volume_label = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/SFXVolumeContainer/SFXVolumeLabel")
	fullscreen_button = get_node_or_null("MainPanel/VBoxContainer/SettingsContainer/FullscreenContainer/FullscreenButton")
	back_button = get_node_or_null("MainPanel/VBoxContainer/BackButton")

func validate_node_references():
	"""Kontrola dostupnosti všetkých potrebných nodov"""
	var missing_nodes = []

	if not master_volume_slider:
		missing_nodes.append("master_volume_slider")
	if not master_volume_label:
		missing_nodes.append("master_volume_label")
	if not music_volume_slider:
		missing_nodes.append("music_volume_slider")
	if not music_volume_label:
		missing_nodes.append("music_volume_label")
	if not sfx_volume_slider:
		missing_nodes.append("sfx_volume_slider")
	if not sfx_volume_label:
		missing_nodes.append("sfx_volume_label")
	if not fullscreen_button:
		missing_nodes.append("fullscreen_button")
	if not back_button:
		missing_nodes.append("back_button")

	if missing_nodes.size() > 0:
		print("⚠️ SettingsMenu: Chýbajúce nody: ", missing_nodes)
	else:
		print("✅ SettingsMenu: Všetky nody sú dostupné")

func update_volume_labels():
	if master_volume_label and master_volume_slider:
		master_volume_label.text = "Hlavná hlasitosť: " + str(int(master_volume_slider.value * 100)) + "%"
	if music_volume_label and music_volume_slider:
		music_volume_label.text = "Hudba: " + str(int(music_volume_slider.value * 100)) + "%"
	if sfx_volume_label and sfx_volume_slider:
		sfx_volume_label.text = "Zvukové efekty: " + str(int(sfx_volume_slider.value * 100)) + "%"

func _on_master_volume_changed(value: float):
	GameManager.update_setting("master_volume", value)
	update_volume_labels()

func _on_music_volume_changed(value: float):
	GameManager.update_setting("music_volume", value)
	update_volume_labels()

func _on_sfx_volume_changed(value: float):
	GameManager.update_setting("sfx_volume", value)
	update_volume_labels()

func _on_fullscreen_toggled(pressed: bool):
	GameManager.update_setting("fullscreen", pressed)

func _on_back_pressed():
	GameManager.go_to_main_menu()

func _input(event):
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
