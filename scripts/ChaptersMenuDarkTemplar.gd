extends Control

# Dark Templar Chapters Menu - Elegantné menu kapitol s maskami a assetmi

# UI References
@onready var chapter_title = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterTitle
@onready var chapter_subtitle = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterSubtitle
@onready var chapter_description = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ChapterDescription
@onready var chapter_image = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ImageContainer/ImageFrame/ChapterImage
@onready var image_frame = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ImageContainer/ImageFrame

# Button References
@onready var prev_button = $MainPanel/NavigationContainer/PrevButton
@onready var next_button = $MainPanel/NavigationContainer/NextButton
@onready var play_button = $MainPanel/NavigationContainer/PlayButton
@onready var back_button = $MainPanel/BackButton
@onready var close_button = $MainPanel/CloseButton

# Label References
@onready var title_label = $MainPanel/TitlePanel/TitleLabel
@onready var play_label = $MainPanel/NavigationContainer/PlayButton/PlayLabel
@onready var back_label = $MainPanel/BackButton/BackLabel
@onready var close_label = $MainPanel/CloseButton/CloseLabel

# Progress References
@onready var progress_label = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ProgressContainer/ProgressLabel
@onready var progress_bar = $MainPanel/ContentContainer/ChapterPanel/ChapterContent/ProgressContainer/ProgressBar

# Chapter Selection settings
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "VII"]

# Chapter data
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "[center][color=#D4AF37][b]Marec 1894[/b][/color][/center]\n\nBúrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu cez temný les k hrôzostrašnému sídlu.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Navigačná šifra a Krvavý nápis[/i][/color]",
		"unlocked": true,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Interior_view_of_ornate_Victorian_horse-drawn_car_514b5c75-3caa-4a98-9002-82c1c9326dc1_0.png"
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "[center][color=#D4AF37][b]Vstup do zámku[/b][/color][/center]\n\nVstup do zámku cez masívnu železnú bránu zdobenú heraldickými symbolmi. Vyriešte krvavý nápis a prejdite skúškou Rádu, aby ste sa dostali dovnútra.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Krvavý nápis a Skúška Rádu[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_medieval_castle_gate_with_weathered_iron__ff9848cd-5fdb-41fe-b04c-027d685a6e1e_0.png"
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "[center][color=#D4AF37][b]Veľká sála[/b][/color][/center]\n\nPrehľadávanie veľkej sály zámku a hľadanie stôp po Van Helsingovi. Rozlúštite obrátený odkaz a vyriešte matematickú hádanku o Isabelle.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Obrátený odkaz a Isabellina hádanka[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Grand_medieval_great_hall_with_vaulted_stone_ceil_ab110728-e42f-4cb0-b8c7-c695e3b75b82_0.png"
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "[center][color=#D4AF37][b]Skrytá chodba[/b][/color][/center]\n\nObjavenie tajného krídla zámku cez úzku kamennú chodbu. Prejdite testom pamäte a vyriešte vampírsku aritmetiku.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Test pamäte a Vampírska aritmetika[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Ancient_stone_castle_corridor_with_damp_moss-cove_d1335a08-f001-429f-928d-da3a0e7319b3_2.png"
	},
	{
		"number": 5,
		"title": "ZOSTUP DO TEMNOTY",
		"description": "[center][color=#D4AF37][b]Kamenné schody[/b][/color][/center]\n\nZostup po úzkych kamenných schodoch do najtemnejších častí zámku. Vyriešte tieňový kód a prejdite testom troch pák.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Tieňový kód a Test troch pák[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Narrow_medieval_stone_staircase_descending_into_c_e226c138-860b-4910-8995-a303e5341756_3.png"
	},
	{
		"number": 6,
		"title": "KONFRONTÁCIA",
		"description": "[center][color=#D4AF37][b]Posledná bitka[/b][/color][/center]\n\nFinálna konfrontácia s temnou mocou. Vyriešte hádanku troch sestier a zvládnite rytmus rituálu, aby ste zachránili Van Helsinga.\n\n[color=#E0E0E0][i]Obsahuje 2 hlavolamy: Tri sestry a Rytmus rituálu[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_stone_sarcophagus_cracking_under_tremendo_eda4a822-6964-41b6-87d0-6091ff5eeebd_2.png"
	},
	{
		"number": 7,
		"title": "EPILÓG",
		"description": "[center][color=#D4AF37][b]Úsvit nového dňa[/b][/color][/center]\n\nZáverečná kapitola príbehu. Sledujte, ako sa príbeh končí a aké následky majú vaše činy.\n\n[color=#E0E0E0][i]Záverečná kapitola bez hlavolamov[/i][/color]",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Magnificent_sunrise_breaking_through_clearing_sto_ae111b3c-e624-4fb0-b07d-b18319925104_1.png"
	}
]

func _ready():
	print("🏰 Dark Templar Chapters Menu načítané")

	# Nastavenie masky
	setup_image_mask()

	# Načítanie progress
	load_chapter_progress()

	# Pripojenie signálov
	connect_signals()

	# Aktualizácia zobrazenia
	update_chapter_display()

	# Aplikovanie gotických fontov
	apply_gothic_fonts()

	# Nastavenie fokusu
	if play_button and is_instance_valid(play_button):
		play_button.grab_focus()

func setup_image_mask():
	"""Nastavenie rámčeka pre obrázky kapitol"""
	# Rámček je už nastavený v scéne ako NinePatchRect
	print("✅ Image frame nastavený")

	# Skontroluj, či sú všetky potrebné nody dostupné
	var missing_nodes = []
	if not title_label: missing_nodes.append("title_label")
	if not chapter_title: missing_nodes.append("chapter_title")
	if not play_button: missing_nodes.append("play_button")

	if missing_nodes.size() > 0:
		print("⚠️ Chýbajúce nody: ", missing_nodes)
	else:
		print("✅ Všetky nody sú dostupné")

func apply_gothic_fonts():
	"""Aplikuje gotické fonty na všetky UI elementy - jednoduchá verzia bez FontLoader"""
	print("🎨 Aplikujem základné gotické fonty...")

	# Title labels
	apply_basic_font_style(title_label, "chapter_title")
	apply_basic_font_style(chapter_title, "chapter_title")
	apply_basic_font_style(chapter_subtitle, "character_names")
	apply_basic_font_style(chapter_description, "narrator_text")
	apply_basic_font_style(progress_label, "ui_elements")

	# Button labels
	apply_basic_font_style(play_label, "ui_elements")
	apply_basic_font_style(back_label, "ui_elements")
	apply_basic_font_style(close_label, "ui_elements")

	# Nastav farby a tieň pre button labely
	for label in [play_label, back_label, close_label, progress_label]:
		if label and is_instance_valid(label):
			label.add_theme_color_override("font_color", Color("#D4AF37"))
			label.add_theme_color_override("font_shadow_color", Color.BLACK)
			label.add_theme_constant_override("shadow_offset_x", 2)
			label.add_theme_constant_override("shadow_offset_y", 2)

	print("✅ Gotické fonty aplikované")

func apply_basic_font_style(label: Control, font_type: String):
	"""Základné nastavenie fontov bez FontLoader"""
	if not label or not is_instance_valid(label):
		return

	match font_type:
		"chapter_title":
			label.add_theme_font_size_override("font_size", 32)
			label.add_theme_color_override("font_color", Color("#D4AF37"))
			label.add_theme_color_override("font_shadow_color", Color.BLACK)
			label.add_theme_constant_override("shadow_offset_x", 3)
			label.add_theme_constant_override("shadow_offset_y", 3)
		"character_names":
			label.add_theme_font_size_override("font_size", 24)
			label.add_theme_color_override("font_color", Color("#D4AF37"))
			label.add_theme_color_override("font_shadow_color", Color.BLACK)
			label.add_theme_constant_override("shadow_offset_x", 2)
			label.add_theme_constant_override("shadow_offset_y", 2)
		"narrator_text":
			label.add_theme_font_size_override("font_size", 18)
			label.add_theme_color_override("font_color", Color("#E0E0E0"))
		"ui_elements":
			label.add_theme_font_size_override("font_size", 20)
			label.add_theme_color_override("font_color", Color("#D4AF37"))

func connect_signals():
	"""Pripojí signály pre buttony"""
	prev_button.pressed.connect(_on_prev_pressed)
	next_button.pressed.connect(_on_next_pressed)
	play_button.pressed.connect(_on_play_pressed)
	back_button.pressed.connect(_on_back_pressed)
	close_button.pressed.connect(_on_back_pressed)

	# Hover efekty
	play_button.mouse_entered.connect(_on_play_button_hover)
	play_button.mouse_exited.connect(_on_play_button_unhover)
	back_button.mouse_entered.connect(_on_back_button_hover)
	back_button.mouse_exited.connect(_on_back_button_unhover)

func load_chapter_progress():
	"""Načíta progress kapitol z GameManager"""
	if GameManager:
		for i in range(chapter_data.size()):
			var chapter = chapter_data[i]
			chapter.unlocked = GameManager.is_chapter_unlocked(chapter.number)
			chapter.completed = chapter.number in GameManager.completed_chapters

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)
	
	# Update title
	var title_text = "EPILÓG" if is_epilogue else "KAPITOLA " + roman_numerals[current_chapter_index]
	chapter_title.text = title_text
	
	# Update content
	chapter_subtitle.text = chapter.title
	chapter_description.text = chapter.description
	
	# Update preview image s maskou
	if ResourceLoader.exists(chapter.image_path):
		chapter_image.texture = load(chapter.image_path)
	else:
		chapter_image.texture = null
	
	# Update button states
	prev_button.disabled = current_chapter_index == 0
	next_button.disabled = current_chapter_index == (chapter_data.size() - 1)
	play_button.disabled = not chapter.unlocked
	
	# Visual feedback pre unlocked/locked
	if chapter.unlocked:
		play_label.text = "HRAŤ" if not chapter.completed else "HRAŤ ZNOVU"
		play_label.modulate = Color.WHITE
	else:
		play_label.text = "UZAMKNUTÉ"
		play_label.modulate = Color(0.5, 0.5, 0.5)

	# Update progress bar
	update_progress_display(chapter)

func update_progress_display(chapter: Dictionary):
	"""Aktualizácia zobrazenia postupu"""
	if not chapter.unlocked:
		progress_label.text = "Postup: Uzamknuté"
		progress_bar.value = 0
		progress_bar.modulate = Color(0.5, 0.5, 0.5)
	elif chapter.completed:
		progress_label.text = "Postup: Dokončené ✓"
		progress_bar.value = 100
		progress_bar.modulate = Color(0.2, 0.8, 0.2)  # Zelená
	else:
		# Pre odomknuté ale nedokončené kapitoly - počítaj dokončené hlavolamy
		var progress_value = 0
		if GameManager and GameManager.completed_puzzles.has(chapter.number):
			var completed_puzzles_count = GameManager.completed_puzzles[chapter.number].size()
			var total_puzzles = 2  # Väčšina kapitol má 2 hlavolamy
			if chapter.number == 7:  # Epilóg nemá hlavolamy
				total_puzzles = 1

			progress_value = int((float(completed_puzzles_count) / float(total_puzzles)) * 100.0)

		if progress_value == 0:
			progress_label.text = "Postup: Začaté"
			progress_value = 10  # Minimálny progress za začatie
		else:
			progress_label.text = "Postup: " + str(progress_value) + "%"

		progress_bar.value = progress_value
		progress_bar.modulate = Color(0.8, 0.6, 0.2)  # Zlatá

func _on_prev_pressed():
	"""Predchádzajúca kapitola"""
	if current_chapter_index > 0:
		AudioManager.play_menu_button_sound()
		current_chapter_index -= 1
		update_chapter_display()

func _on_next_pressed():
	"""Nasledujúca kapitola"""
	if current_chapter_index < chapter_data.size() - 1:
		AudioManager.play_menu_button_sound()
		current_chapter_index += 1
		update_chapter_display()

func _on_play_pressed():
	"""Spustenie kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	if chapter.unlocked:
		AudioManager.play_menu_button_sound()
		var scene_path = "res://scenes/Chapter" + str(chapter.number) + ".tscn"
		get_tree().change_scene_to_file(scene_path)

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Klávesové skratky"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_left"):
		_on_prev_pressed()
	elif event.is_action_pressed("ui_right"):
		_on_next_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()

# Hover efekty
func _on_play_button_hover():
	"""Hover efekt pre play button"""
	var tween = create_tween()
	tween.tween_property(play_label, "modulate", Color(1.2, 1.2, 1.2), 0.2)

func _on_play_button_unhover():
	"""Unhover efekt pre play button"""
	var tween = create_tween()
	tween.tween_property(play_label, "modulate", Color.WHITE, 0.2)

func _on_back_button_hover():
	"""Hover efekt pre back button"""
	var tween = create_tween()
	tween.tween_property(back_label, "modulate", Color(1.2, 1.2, 1.2), 0.2)

func _on_back_button_unhover():
	"""Unhover efekt pre back button"""
	var tween = create_tween()
	tween.tween_property(back_label, "modulate", Color.WHITE, 0.2)
