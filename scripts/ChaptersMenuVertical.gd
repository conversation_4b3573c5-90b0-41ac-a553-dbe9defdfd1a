extends Control

# 🏰 VERTIKÁLNY CHAPTERS MENU - DARK TEMPLAR ŠTÝL
# Inš<PERSON>rovaný obrázkom s vertikálnym layoutom

# UI References
@onready var chapter_title = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterTitle
@onready var chapter_subtitle = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterSubtitle
@onready var chapter_image = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/ImageContainer/ImageFrame/ChapterImage
@onready var chapter_description = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ChapterDescription
@onready var progress_label = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ProgressContainer/ProgressLabel
@onready var progress_bar = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ProgressContainer/ProgressBar
@onready var play_button = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton
@onready var play_label = $MainPanel/ContentContainer/ChapterDetailPanel/ChapterDetailContent/InfoContainer/ButtonContainer/PlayButton/PlayLabel
@onready var back_button = $MainPanel/ContentContainer/BottomButtonsContainer/BackButton

# Chapter buttons
@onready var chapter_buttons = [
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter1Button,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter2Button,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter3Button,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter4Button,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter5Button,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter6Button,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/EpilogueButton
]

# Chapter labels
@onready var chapter_labels = [
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter1Button/Chapter1Label,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter2Button/Chapter2Label,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter3Button/Chapter3Label,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter4Button/Chapter4Label,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter5Button/Chapter5Label,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/Chapter6Button/Chapter6Label,
	$MainPanel/ContentContainer/ChapterListPanel/ChapterListContainer/ChapterButtons/EpilogueButton/EpilogueLabel
]

# Chapter Selection settings
var current_chapter_index = 0
var roman_numerals = ["I", "II", "III", "IV", "V", "VI", "Epilóg"]

# Chapter data
var chapter_data = [
	{
		"number": 1,
		"title": "BÚRLIVÁ CESTA",
		"description": "Marec 1894. Búrlivá cesta cez karpatské horstvo k Van Helsingovmu zámku. Rozlúštite šifru a nájdite správnu cestu cez temný les k hrôzostrašnému sídlu.",
		"unlocked": true,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Stormy_night_journey_through_Carpathian_mountains_b5c7e8b1-b8a4-4b8e-9c2d-f3e1a7b9c4d6_0.png"
	},
	{
		"number": 2,
		"title": "BRÁNA ZÁMKU",
		"description": "Vstup do zámku cez masívnu železnú bránu zdobenú heraldickými symbolmi. Vyriešte krvavý nápis a prejdite skúškou Rádu, aby ste sa dostali dovnútra.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Massive_iron_castle_gate_with_heraldic_symbols_an_f8e2d1c3-a5b6-4e7f-9c8d-e1f2a3b4c5d6_0.png"
	},
	{
		"number": 3,
		"title": "PÁTRANIE V ZÁMKU",
		"description": "Prehľadávanie veľkej sály zámku a hľadanie stôp po Van Helsingovi. Rozlúštite obrátený odkaz a vyriešte matematickú hádanku.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Grand_medieval_great_hall_with_vaulted_stone_ceil_ab110728-e42f-4cb0-b8c7-c695e3b75b82_0.png"
	},
	{
		"number": 4,
		"title": "TAJNÉ KRÍDLO",
		"description": "Objavenie skrytého krídla zámku s tajomnou knižnicou. Vyriešte test pamäte a vampírsku aritmetiku.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Hidden_wing_of_castle_with_mysterious_library_d_7f8e9a1b-c2d3-4e5f-6a7b-8c9d0e1f2a3b_0.png"
	},
	{
		"number": 5,
		"title": "TEMNÉ TAJOMSTVÁ",
		"description": "Odhalenie temných tajomstiev Van Helsingovej minulosti v jeho súkromných komnatách.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Van_Helsings_private_chambers_revealing_dark_sec_4e5f6a7b-8c9d-0e1f-2a3b-4c5d6e7f8a9b_0.png"
	},
	{
		"number": 6,
		"title": "KONFRONTÁCIA",
		"description": "Finálna konfrontácia s Van Helsingom v jeho laboratóriu. Vyriešte logickú hádanku troch sestier a rytmus rituálu.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Final_confrontation_in_Van_Helsings_laboratory_1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d_0.png"
	},
	{
		"number": 7,
		"title": "EPILÓG",
		"description": "Záverečné odhalenie pravdy o Van Helsingovi a ukončenie príbehu.",
		"unlocked": false,
		"completed": false,
		"image_path": "res://assets/Kapitoly_VISUALS/vlado_13856_Epilogue_revealing_the_truth_about_Van_Helsing_2b3c4d5e-6f7a-8b9c-0d1e-2f3a4b5c6d7e_0.png"
	}
]

func _ready():
	print("🏰 Vertikálny Dark Templar Chapters Menu načítané")
	
	# Načítanie progress
	load_chapter_progress()
	
	# Pripojenie signálov
	connect_signals()
	
	# Aktualizácia zobrazenia
	update_chapter_display()
	
	# Aplikovanie štýlov
	apply_gothic_fonts()
	
	# Nastavenie fokusu
	if chapter_buttons[0] and is_instance_valid(chapter_buttons[0]):
		chapter_buttons[0].grab_focus()

func load_chapter_progress():
	"""Načítanie postupu z GameManager"""
	for i in range(chapter_data.size()):
		var chapter_num = i + 1
		if GameManager:
			chapter_data[i].unlocked = GameManager.is_chapter_unlocked(chapter_num)
			chapter_data[i].completed = chapter_num in GameManager.completed_chapters

func connect_signals():
	"""Pripojenie signálov pre buttony"""
	for i in range(chapter_buttons.size()):
		if chapter_buttons[i]:
			chapter_buttons[i].pressed.connect(_on_chapter_button_pressed.bind(i))
	
	if play_button: play_button.pressed.connect(_on_play_pressed)
	if back_button: back_button.pressed.connect(_on_back_pressed)

func apply_gothic_fonts():
	"""Aplikovanie gotických fontov"""
	# Bezpečné aplikovanie fontov bez FontLoader
	if chapter_title:
		chapter_title.add_theme_font_size_override("font_size", 28)
		chapter_title.add_theme_color_override("font_color", Color("#D4AF37"))

	if chapter_subtitle:
		chapter_subtitle.add_theme_font_size_override("font_size", 24)
		chapter_subtitle.add_theme_color_override("font_color", Color("#D4AF37"))

	if play_label:
		play_label.add_theme_font_size_override("font_size", 18)
		play_label.add_theme_color_override("font_color", Color.WHITE)

	# Chapter list fonts
	for label in chapter_labels:
		if label:
			label.add_theme_font_size_override("font_size", 16)
			label.add_theme_color_override("font_color", Color("#F5F5DC"))

func update_chapter_display():
	"""Aktualizácia zobrazenia kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	var is_epilogue = current_chapter_index == (chapter_data.size() - 1)
	
	# Update title
	var title_text = "EPILÓG" if is_epilogue else "KAPITOLA " + roman_numerals[current_chapter_index]
	chapter_title.text = title_text
	
	# Update content
	chapter_subtitle.text = chapter.title
	chapter_description.text = chapter.description
	
	# Update preview image
	if ResourceLoader.exists(chapter.image_path):
		chapter_image.texture = load(chapter.image_path)
	else:
		chapter_image.texture = null
	
	# Update button states
	play_button.disabled = not chapter.unlocked
	
	# Visual feedback pre unlocked/locked
	if chapter.unlocked:
		play_label.text = "HRAŤ" if not chapter.completed else "HRAŤ ZNOVU"
		play_label.modulate = Color.WHITE
	else:
		play_label.text = "UZAMKNUTÉ"
		play_label.modulate = Color(0.5, 0.5, 0.5)

	# Update progress bar
	update_progress_display(chapter)
	
	# Update chapter list highlighting
	update_chapter_list_highlighting()

func update_progress_display(chapter: Dictionary):
	"""Aktualizácia zobrazenia postupu"""
	if not chapter.unlocked:
		progress_label.text = "Postup: Uzamknuté"
		progress_bar.value = 0
		progress_bar.modulate = Color(0.5, 0.5, 0.5)
	elif chapter.completed:
		progress_label.text = "Postup: Dokončené ✓"
		progress_bar.value = 100
		progress_bar.modulate = Color(0.2, 0.8, 0.2)  # Zelená
	else:
		# Pre odomknuté ale nedokončené kapitoly
		var progress_value = 0
		if GameManager and GameManager.completed_puzzles.has(chapter.number):
			var completed_puzzles_count = GameManager.completed_puzzles[chapter.number].size()
			var total_puzzles = 2  # Väčšina kapitol má 2 hlavolamy
			if chapter.number == 7:  # Epilóg nemá hlavolamy
				total_puzzles = 1

			progress_value = int((float(completed_puzzles_count) / float(total_puzzles)) * 100.0)

		if progress_value == 0:
			progress_label.text = "Postup: Začaté"
			progress_value = 10  # Minimálny progress za začatie
		else:
			progress_label.text = "Postup: " + str(progress_value) + "%"

		progress_bar.value = progress_value
		progress_bar.modulate = Color(0.8, 0.6, 0.2)  # Zlatá

func update_chapter_list_highlighting():
	"""Aktualizácia zvýraznenia v zozname kapitol"""
	for i in range(chapter_labels.size()):
		if chapter_labels[i]:
			var chapter = chapter_data[i]
			
			# Zvýraznenie aktuálnej kapitoly
			if i == current_chapter_index:
				chapter_labels[i].modulate = Color("#D4AF37")  # Zlatá
				chapter_labels[i].text = "► " + get_chapter_display_name(i)
			else:
				# Farba podľa stavu
				if chapter.completed:
					chapter_labels[i].modulate = Color(0.2, 0.8, 0.2)  # Zelená
					chapter_labels[i].text = "✓ " + get_chapter_display_name(i)
				elif chapter.unlocked:
					chapter_labels[i].modulate = Color("#F5F5DC")  # Krémová
					chapter_labels[i].text = "▶ " + get_chapter_display_name(i)
				else:
					chapter_labels[i].modulate = Color(0.5, 0.5, 0.5)  # Sivá
					chapter_labels[i].text = "🔒 " + get_chapter_display_name(i)

func get_chapter_display_name(index: int) -> String:
	"""Získanie názvu kapitoly pre zobrazenie"""
	var chapter = chapter_data[index]
	if index == chapter_data.size() - 1:
		return "Epilóg - " + chapter.title
	else:
		return "Kapitola " + roman_numerals[index] + " - " + chapter.title

func _on_chapter_button_pressed(chapter_index: int):
	"""Kliknutie na kapitolu v zozname"""
	if AudioManager: AudioManager.play_menu_button_sound()
	current_chapter_index = chapter_index
	update_chapter_display()

func _on_play_pressed():
	"""Spustenie kapitoly"""
	var chapter = chapter_data[current_chapter_index]
	if chapter.unlocked:
		if AudioManager: AudioManager.play_menu_button_sound()
		var scene_path = "res://scenes/Chapter" + str(chapter.number) + ".tscn"
		get_tree().change_scene_to_file(scene_path)
	else:
		# Zvuk chyby pre uzamknuté kapitoly
		if AudioManager: AudioManager.play_puzzle_error_sound()

func _on_back_pressed():
	"""Návrat do hlavného menu"""
	if AudioManager: AudioManager.play_menu_button_sound()
	get_tree().change_scene_to_file("res://scenes/MainMenu.tscn")

func _input(event):
	"""Klávesové skratky"""
	if event.is_action_pressed("ui_cancel"):
		_on_back_pressed()
	elif event.is_action_pressed("ui_accept"):
		_on_play_pressed()
