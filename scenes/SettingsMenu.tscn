[gd_scene load_steps=8 format=3 uid="uid://dx0voaqkqxqxq"]

[ext_resource type="Script" uid="uid://b46jy7ji0ujwi" path="res://scripts/SettingsMenu.gd" id="1_settings"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_bg"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Big_Panel.png" id="3_panel"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Normal.png" id="4_button_normal"]
[ext_resource type="Texture2D" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu/Buttons/Button Hover.png" id="5_button_hover"]
[ext_resource type="Theme" path="res://themes/DarkTemplarTheme.tres" id="6_theme"]

[node name="SettingsMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
theme = ExtResource("6_theme")
script = ExtResource("1_settings")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
texture = ExtResource("2_bg")
expand_mode = 1
stretch_mode = 6

[node name="MainPanel" type="NinePatchRect" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -350.0
offset_top = -400.0
offset_right = 350.0
offset_bottom = 400.0
texture = ExtResource("3_panel")
patch_margin_left = 25
patch_margin_top = 25
patch_margin_right = 25
patch_margin_bottom = 25

[node name="VBoxContainer" type="VBoxContainer" parent="MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "NASTAVENIA"
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="SettingsContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="MasterVolumeContainer" type="VBoxContainer" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2

[node name="MasterVolumeLabel" type="Label" parent="VBoxContainer/SettingsContainer/MasterVolumeContainer"]
layout_mode = 2
text = "Hlavná hlasitosť: 100%"

[node name="MasterVolumeSlider" type="HSlider" parent="VBoxContainer/SettingsContainer/MasterVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="Spacer2" type="Control" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="MusicVolumeContainer" type="VBoxContainer" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2

[node name="MusicVolumeLabel" type="Label" parent="VBoxContainer/SettingsContainer/MusicVolumeContainer"]
layout_mode = 2
text = "Hudba: 100%"

[node name="MusicVolumeSlider" type="HSlider" parent="VBoxContainer/SettingsContainer/MusicVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="Spacer3" type="Control" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="SFXVolumeContainer" type="VBoxContainer" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2

[node name="SFXVolumeLabel" type="Label" parent="VBoxContainer/SettingsContainer/SFXVolumeContainer"]
layout_mode = 2
text = "Zvukové efekty: 100%"

[node name="SFXVolumeSlider" type="HSlider" parent="VBoxContainer/SettingsContainer/SFXVolumeContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 50)

[node name="Spacer4" type="Control" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="FullscreenContainer" type="HBoxContainer" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2

[node name="FullscreenLabel" type="Label" parent="VBoxContainer/SettingsContainer/FullscreenContainer"]
layout_mode = 2
size_flags_horizontal = 3
text = "Celá obrazovka:"

[node name="FullscreenButton" type="CheckButton" parent="VBoxContainer/SettingsContainer/FullscreenContainer"]
layout_mode = 2

[node name="Spacer5" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 30)

[node name="BackButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)
text = "Späť"
