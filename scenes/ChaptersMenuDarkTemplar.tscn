[gd_scene load_steps=13 format=3 uid="uid://b4m1pyg5e32rq"]

[ext_resource type="Script" uid="uid://w3q2cve56lgg" path="res://scripts/ChaptersMenuMobile.gd" id="1_script"]
[ext_resource type="Texture2D" uid="uid://bebx3tdqwiil" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Main Menu/BQ.png" id="2_background"]
[ext_resource type="Texture2D" uid="uid://csjarpkilivm2" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Title Description.png" id="3_title_panel"]
[ext_resource type="Texture2D" uid="uid://dm8x4jmnc17xd" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Mask Img.png" id="4_image_mask"]
[ext_resource type="Texture2D" uid="uid://bi1ibjp7lvcae" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Select/Button Normal.png" id="5_chapter_button_normal"]
[ext_resource type="Texture2D" uid="uid://brttdrph0dxax" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Select/Button Hover.png" id="6_chapter_button_hover"]
[ext_resource type="Texture2D" uid="uid://h4fgxwlt7858" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Normal.png" id="9_button_normal"]
[ext_resource type="Texture2D" uid="uid://6hx86f2x1kth" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/Buttons/Button Hover.png" id="10_button_hover"]
[ext_resource type="Texture2D" uid="uid://0u43hx2s8d5a" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Game Menu & Pause/TextField.png" id="11_text_field"]
[ext_resource type="Texture2D" uid="uid://co54yhnrac17m" path="res://assets/UI MMORPG Dark Templar Wenrexa/UI MMORPG Dark Templar/Button Close Panel/Button Close Normal.png" id="12_close_button"]

[sub_resource type="LabelSettings" id="LabelSettings_title"]
font_size = 28
font_color = Color(0.831, 0.686, 0.216, 1)
outline_size = 2
outline_color = Color(0.2, 0.1, 0.05, 1)
shadow_color = Color(0, 0, 0, 0.8)
shadow_offset = Vector2(2, 2)

[sub_resource type="LabelSettings" id="LabelSettings_chapter"]
font_size = 24
font_color = Color(0.9, 0.8, 0.6, 1)
outline_size = 1
outline_color = Color(0.2, 0.1, 0.05, 1)

[node name="ChaptersMenuMobile" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_script")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_background")
expand_mode = 1
stretch_mode = 6

[node name="MainContainer" type="VBoxContainer" parent="Background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -61.0
offset_top = 43.0
offset_right = 61.0
offset_bottom = 42.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleContainer" type="HBoxContainer" parent="Background/MainContainer"]
custom_minimum_size = Vector2(0, 80)
layout_mode = 2

[node name="TitlePanel" type="NinePatchRect" parent="Background/MainContainer/TitleContainer"]
layout_mode = 2
size_flags_horizontal = 3
texture = ExtResource("3_title_panel")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="TitleLabel" type="Label" parent="Background/MainContainer/TitleContainer/TitlePanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "KAPITOLY"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1
vertical_alignment = 1

[node name="CloseButton" type="TextureButton" parent="Background/MainContainer/TitleContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
texture_normal = ExtResource("12_close_button")

[node name="Spacer1" type="Control" parent="Background/MainContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="ContentArea" type="HBoxContainer" parent="Background/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ChapterListContainer" type="VBoxContainer" parent="Background/MainContainer/ContentArea"]
custom_minimum_size = Vector2(280, 0)
layout_mode = 2
size_flags_horizontal = 3

[node name="ChapterListTitle" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer"]
layout_mode = 2
text = "ZOZNAM KAPITOL"
label_settings = SubResource("LabelSettings_chapter")
horizontal_alignment = 1

[node name="ChapterListSpacer" type="Control" parent="Background/MainContainer/ContentArea/ChapterListContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="ChapterButtons" type="VBoxContainer" parent="Background/MainContainer/ContentArea/ChapterListContainer"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
size_flags_vertical = 3

[node name="Chapter2Button" type="TextureButton" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("5_chapter_button_normal")
texture_hover = ExtResource("6_chapter_button_hover")
stretch_mode = 1

[node name="Chapter2Label" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter2Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_right = -15.0
grow_horizontal = 2
grow_vertical = 2
text = "II. Brána zámku"
vertical_alignment = 1

[node name="Chapter3Button" type="TextureButton" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("5_chapter_button_normal")
texture_hover = ExtResource("6_chapter_button_hover")
stretch_mode = 1

[node name="Chapter3Label" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter3Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_right = -15.0
grow_horizontal = 2
grow_vertical = 2
text = "III. Pátranie v zámku"
vertical_alignment = 1

[node name="Chapter4Button" type="TextureButton" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("5_chapter_button_normal")
texture_hover = ExtResource("6_chapter_button_hover")
stretch_mode = 1

[node name="Chapter4Label" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter4Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_right = -15.0
grow_horizontal = 2
grow_vertical = 2
text = "IV. Tajné krídlo"
vertical_alignment = 1

[node name="Chapter5Button" type="TextureButton" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("5_chapter_button_normal")
texture_hover = ExtResource("6_chapter_button_hover")
stretch_mode = 1

[node name="Chapter5Label" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter5Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_right = -15.0
grow_horizontal = 2
grow_vertical = 2
text = "V. Temné tajomstvá"
vertical_alignment = 1

[node name="Chapter6Button" type="TextureButton" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("5_chapter_button_normal")
texture_hover = ExtResource("6_chapter_button_hover")
stretch_mode = 1

[node name="Chapter6Label" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter6Button"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_right = -15.0
grow_horizontal = 2
grow_vertical = 2
text = "VI. Konfrontácia"
vertical_alignment = 1

[node name="EpilogueButton" type="TextureButton" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"]
custom_minimum_size = Vector2(0, 45)
layout_mode = 2
size_flags_horizontal = 3
texture_normal = ExtResource("5_chapter_button_normal")
texture_hover = ExtResource("6_chapter_button_hover")
stretch_mode = 1

[node name="EpilogueLabel" type="Label" parent="Background/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/EpilogueButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 15.0
offset_right = -15.0
grow_horizontal = 2
grow_vertical = 2
text = "Epilóg"
vertical_alignment = 1

[node name="ContentSpacer" type="Control" parent="Background/MainContainer/ContentArea"]
custom_minimum_size = Vector2(30, 0)
layout_mode = 2

[node name="ChapterDetailContainer" type="VBoxContainer" parent="Background/MainContainer/ContentArea"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ChapterImageContainer" type="CenterContainer" parent="Background/MainContainer/ContentArea/ChapterDetailContainer"]
custom_minimum_size = Vector2(0, 300)
layout_mode = 2

[node name="ChapterImageMask" type="NinePatchRect" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterImageContainer"]
custom_minimum_size = Vector2(280, 280)
layout_mode = 2
texture = ExtResource("4_image_mask")
patch_margin_left = 30
patch_margin_top = 30
patch_margin_right = 30
patch_margin_bottom = 30

[node name="ChapterImage" type="TextureRect" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterImageContainer/ChapterImageMask"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 30.0
offset_top = 30.0
offset_right = -30.0
offset_bottom = -30.0
grow_horizontal = 2
grow_vertical = 2
stretch_mode = 6

[node name="ChapterInfoSpacer" type="Control" parent="Background/MainContainer/ContentArea/ChapterDetailContainer"]
custom_minimum_size = Vector2(0, 20)
layout_mode = 2

[node name="ChapterInfoPanel" type="NinePatchRect" parent="Background/MainContainer/ContentArea/ChapterDetailContainer"]
layout_mode = 2
size_flags_vertical = 3
texture = ExtResource("11_text_field")
patch_margin_left = 20
patch_margin_top = 20
patch_margin_right = 20
patch_margin_bottom = 20

[node name="InfoContainer" type="VBoxContainer" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="ChapterTitle" type="Label" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer"]
layout_mode = 2
text = "KAPITOLA I"
label_settings = SubResource("LabelSettings_title")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer"]
custom_minimum_size = Vector2(0, 10)
layout_mode = 2

[node name="ChapterSubtitle" type="Label" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer"]
layout_mode = 2
text = "BÚRLIVÁ CESTA"
label_settings = SubResource("LabelSettings_chapter")
horizontal_alignment = 1

[node name="Spacer2" type="Control" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer"]
custom_minimum_size = Vector2(0, 15)
layout_mode = 2

[node name="ChapterDescription" type="RichTextLabel" parent="Background/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "Popis kapitoly bude tu..."
fit_content = true

[node name="BottomSpacer" type="Control" parent="Background/MainContainer"]
custom_minimum_size = Vector2(0, 30)
layout_mode = 2

[node name="BottomButtonsContainer" type="HBoxContainer" parent="Background/MainContainer"]
custom_minimum_size = Vector2(0, 70)
layout_mode = 2

[node name="BackButton" type="TextureButton" parent="Background/MainContainer/BottomButtonsContainer"]
custom_minimum_size = Vector2(150, 60)
layout_mode = 2
texture_normal = ExtResource("9_button_normal")
texture_hover = ExtResource("10_button_hover")
stretch_mode = 0

[node name="BackLabel" type="Label" parent="Background/MainContainer/BottomButtonsContainer/BackButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "SPÄŤ"
label_settings = SubResource("LabelSettings_chapter")
horizontal_alignment = 1
vertical_alignment = 1

[node name="ButtonSpacer" type="Control" parent="Background/MainContainer/BottomButtonsContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="PlayButton" type="TextureButton" parent="Background/MainContainer/BottomButtonsContainer"]
custom_minimum_size = Vector2(150, 60)
layout_mode = 2
texture_normal = ExtResource("9_button_normal")
texture_hover = ExtResource("10_button_hover")
stretch_mode = 0

[node name="PlayLabel" type="Label" parent="Background/MainContainer/BottomButtonsContainer/PlayButton"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
text = "HRAŤ"
label_settings = SubResource("LabelSettings_chapter")
horizontal_alignment = 1
vertical_alignment = 1

[node name="SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label" type="Label" parent="Background"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -104.0
offset_top = 21.0
offset_right = 183.0
offset_bottom = 42.0
grow_horizontal = 2
grow_vertical = 2
text = "I. Búrlivá cesta"
vertical_alignment = 1
