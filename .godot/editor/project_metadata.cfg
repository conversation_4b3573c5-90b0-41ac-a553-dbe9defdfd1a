[editor_metadata]

executable_path="/Users/<USER>/Desktop/Godot 2.app/Contents/MacOS/Godot"

[recent_files]

scenes=["res://scenes/FontTestScene.tscn", "res://scenes/DialogueSystem.tscn", "res://scenes/ChapterProgressionTest.tscn", "res://scenes/ChaptersMenuNew.tscn", "res://scenes/ChaptersMenuDarkTemplar.tscn", "res://scenes/ChaptersMenu.tscn", "res://scenes/MemoryTestPuzzle.tscn", "res://scenes/FontVariationTest.tscn", "res://scenes/Chapter3.tscn", "res://scenes/Chapter6.tscn"]
scripts=["res://scripts/ChaptersMenuMobile.gd", "res://scripts/ChaptersMenuDarkTemplar.gd", "res://scripts/ChaptersMenu.gd", "res://scripts/MainMenu.gd", "res://scripts/font_loader.gd", "res://scripts/MemoryTestPuzzle.gd", "res://scripts/DialogueSystem.gd", "res://scripts/CaesarCipherPuzzle.gd", "res://README.md", "res://scripts/Chapter.gd"]

[uid_upgrade_tool]

run_on_restart=false
resave_paths=PackedStringArray()

[dialog_bounds]

project_settings=Rect2(528, 418, 2400, 1400)
export=Rect2(800, 540, 1800, 1200)

[quick_open_dialog]

last_mode=1
