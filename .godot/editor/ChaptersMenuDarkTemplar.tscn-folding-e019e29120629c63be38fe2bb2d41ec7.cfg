[folding]

node_unfolds=[NodePath("."), PackedStringArray("Layout"), NodePath("Background"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/TitleContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/TitleContainer/TitlePanel"), PackedStringArray("Layout", "Patch Margin"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/TitleContainer/TitlePanel/TitleLabel"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/TitleContainer/CloseButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/Spacer1"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterListTitle"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterListSpacer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter2Button"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter2Button/Chapter2Label"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter3Button"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter3Button/Chapter3Label"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter4Button"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter4Button/Chapter4Label"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter5Button"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter5Button/Chapter5Label"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter6Button"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/Chapter6Button/Chapter6Label"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/EpilogueButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterListContainer/ChapterButtons/EpilogueButton/EpilogueLabel"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ContentSpacer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterImageContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterImageContainer/ChapterImageMask"), PackedStringArray("Layout", "Patch Margin"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterImageContainer/ChapterImageMask/ChapterImage"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoSpacer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel"), PackedStringArray("Layout", "Patch Margin"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/ChapterTitle"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/Spacer1"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/ChapterSubtitle"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/Spacer2"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/ContentArea/ChapterDetailContainer/ChapterInfoPanel/InfoContainer/ChapterDescription"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomSpacer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomButtonsContainer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomButtonsContainer/BackButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomButtonsContainer/BackButton/BackLabel"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomButtonsContainer/ButtonSpacer"), PackedStringArray("Layout"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomButtonsContainer/PlayButton"), PackedStringArray("Layout", "Textures"), NodePath("SafeArea_MainContainer_ContentArea_ChapterListContainer_ChapterButtons_Chapter1Button#Chapter1Label/SafeArea/MainContainer/BottomButtonsContainer/PlayButton/PlayLabel"), PackedStringArray("Layout")]
resource_unfolds=["res://scenes/ChaptersMenuDarkTemplar.tscn::LabelSettings_title", PackedStringArray("Resource", "Font", "Outline", "Shadow"), "res://scenes/ChaptersMenuDarkTemplar.tscn::LabelSettings_chapter", PackedStringArray("Resource", "Font", "Outline")]
nodes_folded=[]
